.tableContainer {
  width: 100%;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Desktop table layout */
.table {
  width: 100%;
  border-collapse: collapse;
  display: table;
}

/* Mobile card layout - hidden by default */
.mobileCards {
  display: none;
}

.table thead {
  background-color: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.table th {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table td {
  color: #1f2937;
  font-size: 14px;
}

.sortableHeader {
  cursor: pointer;
  user-select: none;
  position: relative;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.sortableHeader:hover {
  background-color: #e2e8f0;
}

.sortableHeader:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
  background-color: #e2e8f0;
}

.sortIcon {
  font-size: 12px;
  color: white;
  min-width: 16px;
  text-align: center;
  display: inline-flex;
  align-items: center;
  gap: 2px;
  margin-left: 8px;
}

.sortPriority {
  font-size: 10px;
  background: #1f2937;
  color: white;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.numberCell {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

.numberHeader {
  text-align: right;
  justify-content: flex-end;
}

.capitalCell {
  font-size: 13px;
  color: #6b7280;
}

.capitalHeader {
  text-align: left;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 48px 16px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.loading {
  color: #6b7280;
  font-size: 16px;
}

.emptyState {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 48px 16px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.emptyState p {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
}

/* Mobile card layout */
.card {
  border-bottom: 1px solid #e2e8f0;
  padding: 16px;
  background: white;
}

.card:last-child {
  border-bottom: none;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.cardTitle {
  font-weight: 600;
  font-size: 16px;
  color: #1f2937;
  margin: 0;
}

.cardSubtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 2px 0 0 0;
}

.cardBody {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.cardField {
  display: flex;
  flex-direction: column;
}

.cardFieldLabel {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 4px;
}

.cardFieldValue {
  font-size: 14px;
  color: #1f2937;
  font-variant-numeric: tabular-nums;
}

.sortControls {
  display: none;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.sortButton {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  font-size: 13px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sortButton:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.sortButton:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
}

.sortButton.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.sortButtonIcon {
  font-size: 10px;
}

/* Responsive breakpoints */
@media (max-width: 768px) {
  .tableContainer {
    overflow-x: auto;
  }

  .table {
    min-width: 600px;
  }

  .table th,
  .table td {
    padding: 8px 12px;
    font-size: 13px;
  }

  .sortableHeader {
    gap: 4px;
  }

  .sortIcon {
    font-size: 10px;
  }
}

@media (max-width: 640px) {
  /* Hide table, show cards */
  .table {
    display: none;
  }

  .mobileCards {
    display: block;
  }

  .sortControls {
    display: flex;
  }

  .tableContainer {
    overflow-x: visible;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .tableContainer {
    background: #1f2937;
    border-color: #374151;
  }

  .table thead {
    background-color: #374151;
    border-bottom-color: #4b5563;
  }

  .table th,
  .table td {
    border-bottom-color: #374151;
  }

  .table th {
    color: #f9fafb;
  }

  .table td {
    color: #e5e7eb;
  }

  .sortableHeader:hover,
  .sortableHeader:focus {
    background-color: #4b5563;
  }

  .sortIcon {
    color: #9ca3af;
  }

  .capitalCell {
    color: #9ca3af;
  }

  .loadingContainer,
  .emptyState {
    background: #1f2937;
    border-color: #374151;
  }

  .loading,
  .emptyState p {
    color: #9ca3af;
  }

  /* Mobile cards dark mode */
  .card {
    background: #1f2937;
    border-bottom-color: #374151;
  }

  .cardTitle {
    color: #f9fafb;
  }

  .cardSubtitle {
    color: #9ca3af;
  }

  .cardFieldLabel {
    color: #9ca3af;
  }

  .cardFieldValue {
    color: #e5e7eb;
  }

  .sortControls {
    background: #374151;
    border-bottom-color: #4b5563;
  }

  .sortButton {
    background: #1f2937;
    border-color: #4b5563;
    color: #e5e7eb;
  }

  .sortButton:hover {
    background: #374151;
    border-color: #6b7280;
  }

  .sortButton.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }
}
