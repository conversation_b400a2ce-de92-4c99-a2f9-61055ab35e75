{"name": "frontend-architecture-sortable-table", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"react": "18.3.1", "react-dom": "18.3.1", "react-icons": "5.4.0", "tailwindcss": "3.4.17"}, "devDependencies": {"@eslint/js": "9.18.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.1.0", "@testing-library/user-event": "14.5.2", "@types/css-modules": "1.0.5", "@types/jest": "29.5.14", "@types/lodash": "4.17.14", "@types/node": "22.10.5", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@vitejs/plugin-react": "4.3.4", "autoprefixer": "10.4.20", "browserslist": "^4.24.3", "eslint": "9.18.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-react": "7.37.3", "eslint-plugin-react-hooks": "5.1.0", "globals": "15.14.0", "husky": "9.1.7", "identity-obj-proxy": "3.0.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-transformer-svg": "2.0.2", "lodash": "4.17.21", "postcss": "8.4.49", "prettier": "3.4.2", "ts-jest": "29.2.5", "ts-node": "10.9.2", "typescript": "5.7.2", "typescript-eslint": "8.19.0", "typescript-plugin-css-modules": "5.1.0", "vite": "6.0.7"}, "scripts": {"dev": "vite", "lint": "eslint src", "lint:fix": "npm run lint -- --fix", "format": "prettier --write .", "test": "jest", "prepare": "husky"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}