.searchContainer {
  margin-bottom: 24px;
}

.label {
  display: block;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  margin-bottom: 8px;
}

.inputContainer {
  position: relative;
  display: flex;
  align-items: center;
}

.searchIcon {
  position: absolute;
  right: 12px;
  font-size: 16px;
  color: #6b7280;
  pointer-events: none;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.input {
  width: 100%;
  padding: 10px 40px 10px 16px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  background: white;
  /* color: #1f2937; */
  color: #fff;
  transition: all 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.input::placeholder {
  color: #e5e7eb;
}

.clearButton {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.clearButton:hover {
  background: #f3f4f6;
  color: #374151;
}

.clearButton:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
}

.helpText {
  margin-top: 6px;
  font-size: 13px;
  color: #6b7280;
}

/* Tablet responsive */
@media (max-width: 768px) {
  .searchContainer {
    margin-bottom: 20px;
  }

  .input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 10px 40px 10px 16px;
  }

  .searchIcon {
    right: 12px;
    font-size: 15px;
  }

  .clearButton {
    right: 12px;
    width: 22px;
    height: 22px;
    font-size: 14px;
  }

  .helpText {
    font-size: 13px;
  }
}

/* Mobile responsive */
@media (max-width: 640px) {
  .searchContainer {
    margin-bottom: 16px;
  }

  .label {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 10px 44px 10px 16px;
    border-radius: 10px;
    border-width: 2px;
  }

  .searchIcon {
    right: 14px;
    font-size: 16px;
  }

  .clearButton {
    right: 14px;
    width: 24px;
    height: 24px;
    font-size: 16px;
  }

  .helpText {
    font-size: 12px;
    margin-top: 8px;
  }
}

@media (max-width: 480px) {
  .input {
    padding: 10px 40px 10px 14px;
  }

  .searchIcon {
    right: 12px;
    font-size: 14px;
  }

  .clearButton {
    right: 12px;
    width: 20px;
    height: 20px;
    font-size: 14px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .label {
    color: #f9fafb;
  }

  .searchIcon {
    color: #9ca3af;
  }

  .input {
    background: #374151;
    border-color: #4b5563;
    color: #e5e7eb;
  }

  .input:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
  }

  .input:disabled {
    background: #1f2937;
    color: #6b7280;
  }

  .input::placeholder {
    color: #e5e7eb;
  }

  .clearButton {
    color: #9ca3af;
  }

  .clearButton:hover {
    background: #4b5563;
    color: #e5e7eb;
  }

  .helpText {
    color: #4b5563;
  }
}
