.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid #e2e8f0;
  margin-top: 16px;
  gap: 16px;
}

.info {
  flex: 1;
}

.infoText {
  color: #6b7280;
  font-size: 14px;
}

.controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  font-size: 14px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 36px;
}

.button:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f9fafb;
  color: #9ca3af;
}

.controlButton {
  font-weight: 500;
}

.buttonIcon {
  font-size: 12px;
  line-height: 1;
}

.buttonText {
  font-size: 13px;
}

.pageInfo {
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.pageText {
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

/* Tablet responsive */
@media (max-width: 768px) {
  .pagination {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .info {
    text-align: center;
    order: 2;
  }

  .controls {
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
    order: 1;
  }

  .button {
    padding: 8px 12px;
    font-size: 13px;
    min-height: 36px;
  }

  .buttonText {
    font-size: 12px;
  }

  .buttonIcon {
    font-size: 10px;
  }

  .pageInfo {
    padding: 0 12px;
  }

  .pageText {
    font-size: 13px;
  }
}

/* Mobile responsive */
@media (max-width: 640px) {
  .pagination {
    padding: 12px 0;
    gap: 16px;
  }

  .controls {
    gap: 4px;
  }

  .button {
    padding: 8px 10px;
    font-size: 12px;
    min-height: 40px;
    min-width: 40px;
  }

  .pageInfo {
    background: #f8fafc;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
  }

  .pageText {
    font-size: 14px;
    font-weight: 600;
  }
}

@media (max-width: 480px) {
  .controls {
    gap: 2px;
  }

  .button {
    padding: 6px 8px;
    min-width: 36px;
    min-height: 36px;
  }

  .buttonText {
    display: none;
  }

  .buttonIcon {
    font-size: 12px;
  }

  .pageInfo {
    padding: 6px 10px;
  }

  .pageText {
    font-size: 13px;
  }

  .infoText {
    font-size: 12px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .pagination {
    border-top-color: #374151;
  }

  .infoText {
    color: #9ca3af;
  }

  .button {
    background: #374151;
    border-color: #4b5563;
    color: #e5e7eb;
  }

  .button:hover:not(:disabled) {
    background: #4b5563;
    border-color: #6b7280;
  }

  .button:disabled {
    background: #1f2937;
    color: #6b7280;
    border-color: #374151;
  }

  .pageText {
    color: #e5e7eb;
  }
}
