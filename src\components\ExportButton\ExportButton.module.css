.exportButton {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.exportButton:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.exportButton:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.3);
}

.exportButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.icon {
  font-size: 16px;
  line-height: 1;
}

/* Focus styles for accessibility */
.exportButton:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.exportButton:focus:not(:disabled) {
  background: #2563eb;
}

.buttonText {
  display: inline;
}

/* Mobile responsive - hide text, show only icon */
@media (max-width: 640px) {
  .exportButton {
    padding: 14px;
    min-width: 40px;
    justify-content: center;
  }

  .buttonText {
    display: none;
  }

  .icon {
    font-size: 18px;
  }
}
